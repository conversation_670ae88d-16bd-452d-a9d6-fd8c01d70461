import { ChangeEvent, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';

import styles from './ProductSearchInput.module.css';

import { ProductType, SearchParamsProps } from '@/types';
import { useProductStore } from '@/apps/shop/stores/useProductStore/useProductStore';
import { buildQueryString } from '@/utils/buildQueryString';
import { SHOP_ROUTES_PATH } from '@/apps/shop/routes/routes';

import { useProductSuggestions } from './useProductSuggestions';
import { Combobox } from '@/libs/ui/Combobox/Combobox';
import { Button } from '@/libs/ui/Button/Button';

export const ProductSearchInput = () => {
  const { query, perPage, updateSearchQueryValue, getSearchProduct } =
    useProductStore();
  const { t } = useTranslation();
  const [selectedProduct, setSelectedProduct] = useState<string>('');

  const navigate = useNavigate();
  const { pathname } = useLocation();
  const [, setQueryParams] = useSearchParams();

  const { searchSuggestions, isSuggestionsLoading } =
    useProductSuggestions(query);

  useEffect(() => {
    setSelectedProduct('');
  }, [pathname]);

  const handleSearchQuery = async (value: string) => {
    const params = {
      query: value.trim(),
      page: 1,
      perPage,
      sortBy: undefined,
      sortOrder: undefined,
    };
    const newQuery = buildQueryString<SearchParamsProps<ProductType>>(params);

    if (pathname !== SHOP_ROUTES_PATH.search) {
      navigate(`${SHOP_ROUTES_PATH.search}?${newQuery}`);

      return;
    }
    getSearchProduct(params, setQueryParams);
  };

  const handleProductSelect = (value: unknown) => {
    const productName = value as string;
    if (productName) {
      setSelectedProduct(productName);
      updateSearchQueryValue(productName);
      handleSearchQuery(productName);
    }
  };

  const handleInputChange = (event: ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    updateSearchQueryValue(value);
    setSelectedProduct('');
  };

  const handleEnterPress = (searchTerm: string) => {
    handleSearchQuery(searchTerm);
  };

  return (
    <div className="relative w-full">
      <Combobox
        value={selectedProduct || query}
        onChange={handleProductSelect}
        isLoading={isSuggestionsLoading}
      >
        <Combobox.Input
          placeholder={t('common.searchProducts')}
          onChange={handleInputChange}
          onEnterPress={handleEnterPress}
          className={styles.searchInput}
          displayValue={(value) => (value as string) || query}
        />

        {searchSuggestions.length > 0 && (
          <Combobox.Options>
            <div className="flex">
              <div className="min-w-72">
                {searchSuggestions.map((suggestion) => (
                  <Combobox.Option key={suggestion} value={suggestion}>
                    <Button variant="unstyled" className={styles.productName}>
                      {suggestion}
                    </Button>
                  </Combobox.Option>
                ))}
              </div>
              <div className="grow border-l border-gray-200 px-3">
                <p className="text-md font-semibold">Buy it again</p>
                <Combobox.Option key={suggestion} value={suggestion}>
                  <Button to={SHOP_ROUTES_PATH.productItem}></Button>
                </Combobox.Option>
              </div>
            </div>
          </Combobox.Options>
        )}
      </Combobox>
    </div>
  );
};
