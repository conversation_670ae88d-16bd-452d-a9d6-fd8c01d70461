import { useEffect, useState, forwardRef } from 'react';
import type { ReactNode } from 'react';
import { Link } from 'react-router-dom';
import { Loader } from '@mantine/core';
import { cva, type VariantProps } from 'class-variance-authority';
import { mergeClasses } from '@/utils/tailwind';

const buttonVariants = cva(
  // Base styles shared by all variants except unstyled
  'relative inline-block overflow-hidden font-medium cursor-pointer border-none transition-colors duration-200 no-underline whitespace-nowrap p-0',
  {
    variants: {
      variant: {
        default:
          'text-[#222] bg-[var(--mantine-color-yellow-5)] rounded-md disabled:opacity-50 disabled:pointer-events-none focus-visible:outline-2 focus-visible:outline-[var(--mantine-color-yellow-6)] focus-visible:outline-offset-0.5',
        secondary:
          'text-white bg-[var(--mantine-color-light-blue-9)] rounded-md disabled:opacity-50 disabled:pointer-events-none focus-visible:outline-2 focus-visible:outline-[var(--mantine-color-yellow-6)] focus-visible:outline-offset-0.5',
        white:
          'text-[#222] bg-white border border-[rgba(34,34,34,0.15)] rounded-md disabled:opacity-50 disabled:pointer-events-none focus-visible:outline-2 focus-visible:outline-[var(--mantine-color-yellow-6)] focus-visible:outline-offset-0.5',
        transparent:
          'bg-transparent text-inherit rounded-md disabled:opacity-50 disabled:pointer-events-none focus-visible:outline-2 focus-visible:outline-[var(--mantine-color-yellow-6)] focus-visible:outline-offset-0.5',
        unstyled:
          'bg-transparent border-none p-0 m-0 font-inherit text-inherit leading-none w-auto min-w-0 h-auto rounded-none overflow-visible whitespace-normal disabled:cursor-not-allowed disabled:opacity-60 focus:outline-none focus-visible:outline-2 focus-visible:outline-[var(--mantine-color-blue-6)] focus-visible:outline-offset-2',
      },
      size: {
        sm: 'h-8',
        md: 'h-10',
      },
      fullWidth: {
        true: 'w-full',
        false: 'w-auto',
      },
    },
    compoundVariants: [
      {
        variant: 'unstyled',
        size: ['sm', 'md'],
        class: 'h-auto', // Override height for unstyled variant
      },
      {
        variant: 'unstyled',
        fullWidth: [true, false],
        class: 'w-auto', // Override width for unstyled variant
      },
    ],
    defaultVariants: {
      variant: 'default',
      size: 'md',
      fullWidth: true,
    },
  },
);

const loaderWrapperVariants = cva(
  'absolute top-0 left-0 right-0 h-full flex items-center justify-center transition-transform duration-300 ease-in-out pointer-events-none',
  {
    variants: {
      loading: {
        true: 'transform translate-y-0 opacity-100',
        false: 'transform -translate-y-full opacity-0',
      },
    },
    defaultVariants: {
      loading: false,
    },
  },
);

const contentWrapperVariants = cva(
  'flex items-center justify-center h-full transition-transform duration-300 ease-in-out',
  {
    variants: {
      loading: {
        true: 'transform translate-y-full opacity-0',
        false: 'transform translate-y-0 opacity-100',
      },
    },
    defaultVariants: {
      loading: false,
    },
  },
);

export type ButtonBaseProps = {
  children: ReactNode;
  loading?: boolean;
  p?: string;
  // Backward compatibility props
  iconOnly?: boolean;
  color?: 'primary' | 'secondary' | 'transparent';
  w?: string | number;
  h?: string | number;
} & VariantProps<typeof buttonVariants>;

type ButtonAsButton = ButtonBaseProps &
  React.ButtonHTMLAttributes<HTMLButtonElement> & {
    href?: never;
    to?: never;
    disabled?: boolean;
  };

type ButtonAsLink = ButtonBaseProps &
  React.AnchorHTMLAttributes<HTMLAnchorElement> & {
    href?: string;
    to?: string;
    disabled?: boolean;
  };

export type ButtonProps = ButtonAsButton | ButtonAsLink;

export const Button = forwardRef<
  HTMLButtonElement | HTMLAnchorElement,
  ButtonProps
>(({ className: extraClassName, ...props }, ref) => {
  const {
    children,
    loading = false,
    disabled = false,
    variant = 'default',
    size = 'md',
    fullWidth = true,
    p = '0 1rem',
    // Backward compatibility props
    iconOnly,
    color,
    w,
    h,
  } = props;

  const [showLoader, setShowLoader] = useState(loading);

  useEffect(() => {
    let timeout: NodeJS.Timeout;
    if (loading) {
      setShowLoader(true);
    } else {
      timeout = setTimeout(() => setShowLoader(false), 300);
    }
    return () => clearTimeout(timeout);
  }, [loading]);

  // Handle backward compatibility for color prop
  const resolvedVariant = color === 'transparent' ? 'transparent' : variant;

  // Handle backward compatibility for iconOnly prop
  if (iconOnly && process.env.NODE_ENV === 'development') {
    console.warn(
      'Button: iconOnly prop is deprecated. Consider using variant="unstyled" or a dedicated IconButton component.',
    );
  }

  const commonClassName = mergeClasses(
    buttonVariants({ variant: resolvedVariant, size, fullWidth }),
    extraClassName,
  );

  // Handle backward compatibility for w, h props and padding
  const styleInline =
    variant === 'unstyled'
      ? {}
      : {
          padding: p,
          ...(w && { width: typeof w === 'number' ? `${w}px` : w }),
          ...(h && { height: typeof h === 'number' ? `${h}px` : h }),
        };

  const loaderWrapperClasses = loaderWrapperVariants({ loading: showLoader });
  const contentWrapperClasses = contentWrapperVariants({ loading: showLoader });

  const content = (
    <>
      <div className={loaderWrapperClasses}>
        <Loader color="#FFF" size="sm" />
      </div>
      <div className={contentWrapperClasses} style={styleInline}>
        {children}
      </div>
    </>
  );

  if ('to' in props && props.to) {
    const { to, onClick, ...rest } = props;
    return (
      <Link
        ref={ref as React.Ref<HTMLAnchorElement>}
        to={to}
        onClick={onClick as React.MouseEventHandler<HTMLAnchorElement>}
        className={commonClassName}
        {...(rest as React.AnchorHTMLAttributes<HTMLAnchorElement>)}
      >
        {content}
      </Link>
    );
  }

  if ('href' in props && props.href) {
    const { href, onClick, ...rest } = props;
    return (
      <a
        ref={ref as React.Ref<HTMLAnchorElement>}
        href={href}
        onClick={onClick as React.MouseEventHandler<HTMLAnchorElement>}
        className={commonClassName}
        {...(rest as React.AnchorHTMLAttributes<HTMLAnchorElement>)}
      >
        {content}
      </a>
    );
  }

  const { onClick, loading: loadingProps, ...rest } = props;

  return (
    <button
      ref={ref as React.Ref<HTMLButtonElement>}
      className={commonClassName}
      onClick={onClick as React.MouseEventHandler<HTMLButtonElement>}
      disabled={loadingProps || disabled}
      {...(rest as React.ButtonHTMLAttributes<HTMLButtonElement>)}
    >
      {content}
    </button>
  );
});

Button.displayName = 'Button';
