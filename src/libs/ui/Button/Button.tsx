import { useEffect, useState, forwardRef } from 'react';
import type { ReactNode } from 'react';
import { Link } from 'react-router-dom';
import { Loader } from '@mantine/core';
import { mergeClasses } from '@/utils/tailwind';

export type ButtonBaseProps = {
  children: ReactNode;
  loading?: boolean;
  variant?: 'default' | 'secondary' | 'white' | 'unstyled';
  p?: string;
  size?: 'sm' | 'md';
};

type ButtonAsButton = ButtonBaseProps &
  React.ButtonHTMLAttributes<HTMLButtonElement> & {
    href?: never;
    to?: never;
    disabled?: boolean;
  };

type ButtonAsLink = ButtonBaseProps &
  React.AnchorHTMLAttributes<HTMLAnchorElement> & {
    href?: string;
    to?: string;
    disabled?: boolean;
  };

export type ButtonProps = ButtonAsButton | ButtonAsLink;

const unstyledClasses =
  'bg-transparent border-none p-0 m-0 font-inherit text-inherit cursor-pointer no-underline inline-block leading-none w-auto min-w-0 h-auto rounded-none overflow-visible whitespace-normal disabled:cursor-not-allowed disabled:opacity-60 focus:outline-none focus-visible:outline-2 focus-visible:outline-[var(--mantine-color-blue-6)] focus-visible:outline-offset-2';
const container =
  'text-[#222] bg-[var(--mantine-color-yellow-5)] relative inline-block overflow-hidden rounded-md h-10 font-medium cursor-pointer border-none transition-colors duration-200 w-full no-underline whitespace-nowrap p-0 disabled:opacity-50 disabled:pointer-events-none focus-visible:outline-2 focus-visible:outline-[var(--mantine-color-yellow-6)] focus-visible:outline-offset-0.5';

export const Button = forwardRef<
  HTMLButtonElement | HTMLAnchorElement,
  ButtonProps
>(({ className: extraClassName, ...props }, ref) => {
  const {
    children,
    loading = false,
    disabled = false,
    variant = 'default',
    p = '0 1rem',
    size = 'md',
  } = props;

  const [showLoader, setShowLoader] = useState(loading);

  useEffect(() => {
    let timeout: NodeJS.Timeout;
    if (loading) {
      setShowLoader(true);
    } else {
      timeout = setTimeout(() => setShowLoader(false), 300);
    }
    return () => clearTimeout(timeout);
  }, [loading]);

  const baseClasses = variant === 'unstyled' ? unstyledClasses : container;

  const variantClasses = mergeClasses({
    'text-white bg-[var(--mantine-color-light-blue-9)]':
      variant === 'secondary',
    'bg-white border border-[rgba(34,34,34,0.15)]': variant === 'white',
    'h-8': size === 'sm' && variant !== 'unstyled',
  });

  const commonClassName = mergeClasses(
    baseClasses,
    variantClasses,
    extraClassName,
  );

  const styleInline = variant === 'unstyled' ? {} : { padding: p };

  const loaderWrapperClasses = mergeClasses(
    'absolute top-0 left-0 right-0 h-full flex items-center justify-center transition-transform duration-300 ease-in-out pointer-events-none',
    showLoader
      ? 'transform translate-y-0 opacity-100'
      : 'transform -translate-y-full opacity-0',
  );

  const contentWrapperClasses = mergeClasses(
    'flex items-center justify-center h-full transition-transform duration-300 ease-in-out',
    showLoader
      ? 'transform translate-y-full opacity-0'
      : 'transform translate-y-0 opacity-100',
  );

  const content = (
    <>
      <div className={loaderWrapperClasses}>
        <Loader color="#FFF" size="sm" />
      </div>
      <div className={contentWrapperClasses} style={styleInline}>
        {children}
      </div>
    </>
  );

  if ('to' in props && props.to) {
    const { to, onClick, ...rest } = props;
    return (
      <Link
        ref={ref as React.Ref<HTMLAnchorElement>}
        to={to}
        onClick={onClick as React.MouseEventHandler<HTMLAnchorElement>}
        className={commonClassName}
        {...(rest as React.AnchorHTMLAttributes<HTMLAnchorElement>)}
      >
        {content}
      </Link>
    );
  }

  if ('href' in props && props.href) {
    const { href, onClick, ...rest } = props;
    return (
      <a
        ref={ref as React.Ref<HTMLAnchorElement>}
        href={href}
        onClick={onClick as React.MouseEventHandler<HTMLAnchorElement>}
        className={commonClassName}
        {...(rest as React.AnchorHTMLAttributes<HTMLAnchorElement>)}
      >
        {content}
      </a>
    );
  }

  const { onClick, loading: loadingProps, ...rest } = props;

  return (
    <button
      ref={ref as React.Ref<HTMLButtonElement>}
      className={commonClassName}
      onClick={onClick as React.MouseEventHandler<HTMLButtonElement>}
      disabled={loadingProps || disabled}
      {...(rest as React.ButtonHTMLAttributes<HTMLButtonElement>)}
    >
      {content}
    </button>
  );
});

Button.displayName = 'Button';
